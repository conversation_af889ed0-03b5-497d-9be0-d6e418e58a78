#include "AsyncRepositoryBase.h"
#include "DatabaseManager.h"
#include "DatabaseWorker.h"
#include <QDebug>

AsyncRepositoryBase::AsyncRepositoryBase(DatabaseManager *dbManager,
                                         const QString &repositoryName,
                                         QObject *parent)
    : QObject(parent)
      , m_dbManager(dbManager)
      , m_repositoryName(repositoryName)
      , m_worker(nullptr) {
    if (!m_dbManager) {
        qWarning() << m_repositoryName << ": DatabaseManager is null";
        return;
    }

    // 创建工作线程（不设置父对象，避免moveToThread问题）
    m_worker = new DatabaseWorker(m_dbManager, nullptr);

    // 连接信号
    connect(m_worker, &DatabaseWorker::taskCompleted,
            this, &AsyncRepositoryBase::onTaskCompleted);
    connect(m_worker, &DatabaseWorker::errorOccurred,
            this, &AsyncRepositoryBase::onWorkerError);
}

AsyncRepositoryBase::~AsyncRepositoryBase() {
    stop();
    // 手动删除worker，因为它没有父对象
    if (m_worker) {
        delete m_worker;
        m_worker = nullptr;
    }
}

void AsyncRepositoryBase::start() {
    if (m_worker) {
        m_worker->start();
    }
}

void AsyncRepositoryBase::stop() {
    if (m_worker) {
        m_worker->stop();
    }
}

bool AsyncRepositoryBase::isValid() const {
    QMutexLocker locker(&m_mutex);
    return m_dbManager && m_dbManager->isConnected() && m_worker;
}

bool AsyncRepositoryBase::isRunning() const {
    return m_worker && m_worker->isRunning();
}

int AsyncRepositoryBase::pendingTaskCount() const {
    return m_worker ? m_worker->pendingTaskCount() : 0;
}

std::unique_ptr<Poco::Data::Session> AsyncRepositoryBase::getThreadSafeSession() {
    if (!m_dbManager) {
        return nullptr;
    }
    return m_dbManager->getThreadSafeSession();
}

const QString &AsyncRepositoryBase::getRepositoryName() const {
    return m_repositoryName;
}

void AsyncRepositoryBase::onTaskCompleted(const QString &description, bool success) {
    emit operationCompleted(description, success);
}

void AsyncRepositoryBase::onWorkerError(const QString &error) {
    emit errorOccurred(error);
}
