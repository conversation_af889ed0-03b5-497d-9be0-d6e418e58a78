#include <QCoreApplication>
#include <QDebug>
#include <QTimer>
#include "DatabaseManager.h"
#include "TestRepository.h"
#include "Test.h"

/**
 * @brief 简化的异步数据库操作示例
 * 
 * 展示使用新的AsyncRepositoryBase基类后，代码变得多么简洁
 */
class SimplifiedAsyncExample : public QObject {
    Q_OBJECT

public:
    SimplifiedAsyncExample(QObject* parent = nullptr) : QObject(parent) {
        setupDatabase();
    }

private slots:
    void runExample() {
        qDebug() << "\n=== 简化的异步数据库操作示例 ===";
        
        // 示例1：基础异步操作（带错误处理）
        demonstrateAsyncWithErrorHandling();
        
        // 示例2：链式异步操作
        QTimer::singleShot(2000, this, &SimplifiedAsyncExample::demonstrateChainedOperations);
        
        // 示例3：批量操作
        QTimer::singleShot(4000, this, &SimplifiedAsyncExample::demonstrateBatchOperations);
        
        // 示例4：高级异步操作
        QTimer::singleShot(6000, this, &SimplifiedAsyncExample::demonstrateAdvancedAsyncOperations);
        
        // 清理和退出
        QTimer::singleShot(8000, this, &SimplifiedAsyncExample::cleanup);
    }

private:
    DatabaseManager* m_dbManager;
    TestRepository* m_repository;

    void setupDatabase() {
        qDebug() << "=== 初始化简化的异步数据库 ===";
        
        m_dbManager = new DatabaseManager(this);
        
        if (!m_dbManager->initialize("simplified_async_example.db", "", 5)) {
            qCritical() << "Failed to initialize database";
            return;
        }
        
        if (!m_dbManager->createTables()) {
            qCritical() << "Failed to create tables";
            return;
        }
        
        m_repository = new TestRepository(m_dbManager, this);

        // 连接信号（可选）
        connect(m_repository, &TestRepository::operationCompleted,
                this, [](const QString& operation, bool success) {
                    qDebug() << "Operation completed:" << operation << "Success:" << success;
                });
        
        m_repository->start();
        qDebug() << "Database initialized successfully";
    }

    void demonstrateAsyncWithErrorHandling() {
        qDebug() << "\n--- 示例1：异步操作与错误处理 ---";
        
        Test test1("异步测试1", "带错误处理的异步操作", 100);
        
        // 新的简化API - 使用AsyncResult包装结果
        m_repository->createTest(test1, [this](const AsyncResult<int>& result) {
            if (result.success) {
                qDebug() << "✓ 异步创建成功，ID:" << result.data;

                // 链式操作：创建成功后立即查询
                m_repository->getTestById(result.data, [](const AsyncResult<std::unique_ptr<Test>>& queryResult) {
                    if (queryResult.success && queryResult.data) {
                        qDebug() << "✓ 异步查询成功:" << queryResult.data->toString();
                    } else {
                        qWarning() << "✗ 异步查询失败:" << queryResult.errorMessage;
                    }
                });
            } else {
                qWarning() << "✗ 异步创建失败:" << result.errorMessage;
            }
        });
    }

    void demonstrateChainedOperations() {
        qDebug() << "\n--- 示例2：链式异步操作 ---";
        
        Test test2("链式测试", "演示链式异步操作", 200);
        
        // 创建 -> 查询 -> 更新 -> 再次查询
        m_repository->createTest(test2, [this](const AsyncResult<int>& createResult) {
            if (!createResult.success) {
                qWarning() << "创建失败:" << createResult.errorMessage;
                return;
            }

            int id = createResult.data;
            qDebug() << "步骤1: 创建成功，ID:" << id;

            // 查询刚创建的记录
            m_repository->getTestById(id, [this, id](const AsyncResult<std::unique_ptr<Test>>& queryResult) {
                if (!queryResult.success) {
                    qWarning() << "查询失败:" << queryResult.errorMessage;
                    return;
                }

                qDebug() << "步骤2: 查询成功:" << queryResult.data->toString();

                // 更新记录
                m_repository->updateTest(id, "链式测试(已更新)", "更新后的描述", 300,
                    [this, id](const AsyncResult<bool>& updateResult) {
                        if (!updateResult.success) {
                            qWarning() << "更新失败:" << updateResult.errorMessage;
                            return;
                        }

                        qDebug() << "步骤3: 更新成功";

                        // 再次查询验证更新
                        m_repository->getTestById(id, [](const AsyncResult<std::unique_ptr<Test>>& finalResult) {
                            if (finalResult.success && finalResult.data) {
                                qDebug() << "步骤4: 最终查询成功:" << finalResult.data->toString();
                            } else {
                                qWarning() << "最终查询失败:" << finalResult.errorMessage;
                            }
                        });
                    });
            });
        });
    }

    void demonstrateBatchOperations() {
        qDebug() << "\n--- 示例3：批量操作 ---";
        
        QList<Test> batchTests;
        for (int i = 1; i <= 3; ++i) {
            batchTests.append(Test(
                QString("批量测试%1").arg(i).toStdString(),
                QString("批量创建的第%1个记录").arg(i).toStdString(),
                i * 100
            ));
        }
        
        m_repository->batchCreateTests(batchTests, [this](const AsyncResult<int>& batchResult) {
            if (batchResult.success) {
                qDebug() << "✓ 批量创建成功，创建了" << batchResult.data << "条记录";

                // 查询所有记录验证
                m_repository->getAllTests([](const AsyncResult<QList<Test>>& allResult) {
                    if (allResult.success) {
                        qDebug() << "✓ 当前总记录数:" << allResult.data.size();
                        for (const Test& test : allResult.data) {
                            qDebug() << "  -" << test.toString();
                        }
                    } else {
                        qWarning() << "✗ 查询所有记录失败:" << allResult.errorMessage;
                    }
                });
            } else {
                qWarning() << "✗ 批量创建失败:" << batchResult.errorMessage;
            }
        });
    }

    void demonstrateAdvancedAsyncOperations() {
        qDebug() << "\n--- 示例4：高级异步操作 ---";

        // 演示分页查询
        m_repository->getTestsPaginated(0, 3, [this](const AsyncResult<QList<Test>>& result) {
            if (result.success) {
                qDebug() << "✓ 分页查询成功，获取到" << result.data.size() << "条记录";
                for (const Test& test : result.data) {
                    qDebug() << "  -" << test.toString();
                }

                // 演示批量更新
                QMap<int, Test> updates;
                if (!result.data.isEmpty()) {
                    Test updatedTest = result.data.first();
                    updatedTest.setName("批量更新测试");
                    updatedTest.setDescription("通过批量更新修改");
                    updates.insert(updatedTest.getId(), updatedTest);

                    m_repository->batchUpdateTests(updates, [](const AsyncResult<int>& updateResult) {
                        if (updateResult.success) {
                            qDebug() << "✓ 批量更新成功，更新了" << updateResult.data << "条记录";
                        } else {
                            qWarning() << "✗ 批量更新失败:" << updateResult.errorMessage;
                        }
                    });
                }
            } else {
                qWarning() << "✗ 分页查询失败:" << result.errorMessage;
            }
        });

        // 获取记录总数
        m_repository->getTestCount([](const AsyncResult<int>& result) {
            if (result.success) {
                qDebug() << "当前数据库中总记录数:" << result.data;
            } else {
                qWarning() << "获取记录总数失败:" << result.errorMessage;
            }
        });
    }

    void cleanup() {
        qDebug() << "\n--- 清理资源 ---";
        
        if (m_repository) {
            qDebug() << "停止Repository工作线程...";
            m_repository->stop();
        }
        
        qDebug() << "简化异步示例完成，退出应用";
        QCoreApplication::quit();
    }
};

int main(int argc, char *argv[]) {
    QCoreApplication app(argc, argv);
    
    SimplifiedAsyncExample example;
    
    // 启动示例
    QTimer::singleShot(100, &example, &SimplifiedAsyncExample::runExample);
    
    return app.exec();
}

#include "SimplifiedAsyncExample.moc"
