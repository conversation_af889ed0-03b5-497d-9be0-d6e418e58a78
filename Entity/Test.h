#ifndef TEST_H
#define TEST_H

#include <string>
#include <QDateTime>
#include "Poco/Tuple.h"

/**
 * @brief Test实体类
 *
 * 对应数据库中的test表，使用POCO ORM进行对象关系映射
 * 使用Poco::Tuple存储数据，支持直接into()操作
 * 不重要的类可以把 Poco::Tuple data 成员变量设置为 public，重要的类就不使用 Poco::Tuple（信息安全角度）
 */
class Test {
public:
    // 定义Tuple类型：id, name, description, value, created_at, updated_at
    typedef Poco::Tuple<int, std::string, std::string, int, std::string, std::string> TestTuple;

    Test();

    Test(const std::string &name, const std::string &description = std::string(), int value = 0);

    Test(int id, const std::string &name, const std::string &description, int value,
         const std::string &createdAt, const std::string &updatedAt);

    ~Test();

    // 使用Poco::Tuple存储所有数据
    TestTuple data;

    // Getter方法 - 通过tuple.get<index>()访问
    int getId() const { return data.get<0>(); }
    const std::string &getName() const { return data.get<1>(); }
    const std::string &getDescription() const { return data.get<2>(); }
    int getValue() const { return data.get<3>(); }
    const std::string &getCreatedAt() const { return data.get<4>(); }
    const std::string &getUpdatedAt() const { return data.get<5>(); }

    // Setter方法 - 通过tuple.set<index>()设置
    void setId(int newId) { data.set<0>(newId); }
    void setName(const std::string &newName) { data.set<1>(newName); }
    void setDescription(const std::string &newDescription) { data.set<2>(newDescription); }
    void setValue(int newValue) { data.set<3>(newValue); }
    void setCreatedAt(const std::string &newCreatedAt) { data.set<4>(newCreatedAt); }
    void setUpdatedAt(const std::string &newUpdatedAt) { data.set<5>(newUpdatedAt); }

    // Qt类型转换方法
    QString getNameAsQString() const { return QString::fromStdString(getName()); }
    QString getDescriptionAsQString() const { return QString::fromStdString(getDescription()); }

    QDateTime getCreatedAtAsQDateTime() const;

    QDateTime getUpdatedAtAsQDateTime() const;

    // 从Qt类型设置
    void setNameFromQString(const QString &qName) { setName(qName.toStdString()); }
    void setDescriptionFromQString(const QString &qDescription) { setDescription(qDescription.toStdString()); }

    /**
     * @brief 更新修改时间为当前时间
     */
    void updateTimestamp();

    /**
     * @brief 检查对象是否有效（ID > 0）
     * @return 有效返回true，无效返回false
     */
    bool isValid() const { return getId() > 0; }

    /**
     * @brief 将对象转换为字符串表示
     * @return 字符串表示
     */
    QString toString() const;

    /**
     * @brief 比较操作符
     */
    bool operator==(const Test &other) const;

    bool operator!=(const Test &other) const;

private:
    /**
     * @brief 初始化时间戳
     */
    void initializeTimestamps();
};

#endif // TEST_H
